using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using HandyControl.Tools.Extension;
using System.Diagnostics;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Windows.Shapes;
using System.Windows.Threading;

namespace Xylia.BnsHelper.ViewModels.Dialogs;

internal partial class MessageDialogViewModel : ObservableObject, IDialogResultable<MessageBoxResult>
{
    #region Fields
    private DispatcherTimer? _autoCloseTimer;
    private DateTime _startTime;
    #endregion

    #region Properties
    [ObservableProperty] private string? _message;
    [ObservableProperty] private MessageBoxButton _buttonType = MessageBoxButton.OK;
    [ObservableProperty] private MessageBoxImage _icon = MessageBoxImage.Information;
    [ObservableProperty] private int _autoCloseMilliseconds = 0;
    [ObservableProperty] private string _okText = "确定";

    // Icon properties for binding
    [ObservableProperty] private Geometry? _iconGeometry;
    [ObservableProperty] private Brush? _iconBrush;

    // Button visibility properties
    [ObservableProperty] private bool _showOkButton;
    [ObservableProperty] private bool _showCancelButton;
    [ObservableProperty] private bool _showYesButton;
    [ObservableProperty] private bool _showNoButton;

    public MessageBoxResult Result { get; set; }
    public Action? CloseAction { get; set; }
    #endregion

    #region Constructor
    public MessageDialogViewModel()
    {
        UpdateButtonVisibility();
        UpdateAutoCloseTimer();
    }
    #endregion

    #region Commands
    [RelayCommand]
    private void Ok()
    {
        StopTimer();
        Result = MessageBoxResult.OK;
        CloseAction?.Invoke();
    }

    [RelayCommand]
    private void Cancel()
    {
        StopTimer();
        Result = MessageBoxResult.Cancel;
        CloseAction?.Invoke();
    }

    [RelayCommand]
    private void Yes()
    {
        StopTimer();
        Result = MessageBoxResult.Yes;
        CloseAction?.Invoke();
    }

    [RelayCommand]
    private void No()
    {
        StopTimer();
        Result = MessageBoxResult.No;
        CloseAction?.Invoke();
    }

    [RelayCommand]
    public void Close()
    {
        StopTimer();
        Result = MessageBoxResult.Cancel;
        CloseAction?.Invoke();
    }
    #endregion

    #region Methods
    public void Initialize()
    {
        UpdateButtonVisibility();
        UpdateAutoCloseTimer();
    }

    public void UpdateIcon()
    {
        try
        {
            // 如果Application.Current为null，延迟执行
            if (Application.Current?.Resources == null)
            {
                return;
            }

            string geometryKey;
            string brushKey;

            switch (Icon)
            {
                case MessageBoxImage.Information:
                    geometryKey = "InfoGeometry";
                    brushKey = "PrimaryBrush";
                    break;
                case MessageBoxImage.Warning:
                    geometryKey = "WarningGeometry";
                    brushKey = "WarningBrush";
                    break;
                case MessageBoxImage.Error:
                    geometryKey = "ErrorGeometry";
                    brushKey = "DangerBrush";
                    break;
                case MessageBoxImage.Question:
                    geometryKey = "QuestionGeometry";
                    brushKey = "InfoBrush";
                    break;
                default:
                    geometryKey = "InfoGeometry";
                    brushKey = "PrimaryBrush";
                    break;
            }

            // 获取几何形状
            if (Application.Current.Resources.Contains(geometryKey) &&
                Application.Current.Resources[geometryKey] is Geometry geo)
            {
                IconGeometry = geo;
            }
            else
            {
                IconGeometry = null;
            }

            // 获取画刷
            if (Application.Current.Resources.Contains(brushKey) &&
                Application.Current.Resources[brushKey] is Brush br)
            {
                IconBrush = br;
            }
            else
            {
                IconBrush = null;
            }
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"[MessageDialogViewModel] UpdateIcon failed: {ex.Message}");
            // 发生异常时，清空图标属性
            IconGeometry = null;
            IconBrush = null;
        }
    }

    private void UpdateButtonVisibility()
    {
        ShowOkButton = ButtonType == MessageBoxButton.OK || ButtonType == MessageBoxButton.OKCancel;
        ShowCancelButton = ButtonType == MessageBoxButton.OKCancel || ButtonType == MessageBoxButton.YesNoCancel;
        ShowYesButton = ButtonType == MessageBoxButton.YesNo || ButtonType == MessageBoxButton.YesNoCancel;
        ShowNoButton = ButtonType == MessageBoxButton.YesNo || ButtonType == MessageBoxButton.YesNoCancel;
    }

    private void UpdateAutoCloseTimer()
    {
        // 先停止现有的定时器
        StopTimer();

        // 只有当 AutoCloseMilliseconds > 0 时才启动新的定时器
        if (AutoCloseMilliseconds > 0)
        {
            StartAutoCloseTimer();
        }
    }

    private void StartAutoCloseTimer()
    {
        if (_autoCloseTimer != null) return; // 避免重复启动

        _startTime = DateTime.Now;
        _autoCloseTimer = new DispatcherTimer
        {
            Interval = TimeSpan.FromMilliseconds(100) // 每100ms检查一次
        };
        _autoCloseTimer.Tick += OnTimerTick;
        _autoCloseTimer.Start();
    }

    private void OnTimerTick(object? sender, EventArgs e)
    {
        var elapsed = (DateTime.Now - _startTime).TotalMilliseconds;

        if (elapsed >= AutoCloseMilliseconds)
        {
            _autoCloseTimer?.Stop();
            Result = MessageBoxResult.OK; // Auto-close returns OK
            CloseAction?.Invoke();
        }
    }

    private void StopTimer()
    {
        _autoCloseTimer?.Stop();
        _autoCloseTimer = null;
    }
    #endregion

    #region Property Changed Handlers
    partial void OnButtonTypeChanged(MessageBoxButton value)
    {
        UpdateButtonVisibility();
    }

    partial void OnAutoCloseMillisecondsChanged(int value)
    {
        UpdateAutoCloseTimer();
    }

    partial void OnIconChanged(MessageBoxImage value)
    {
        UpdateIcon();
    }
    #endregion
}
