﻿using CommunityToolkit.Mvvm.ComponentModel;
using System.IO;
using Xylia.BnsHelper.Models;
using Xylia.BnsHelper.Resources;
using Xylia.BnsHelper.Services.ApiEndpoints;
using Xylia.BnsHelper.ViewModels.Pages;
using Xylia.Preview.Data.Engine.DatData;
using Xylia.Preview.Data.Models.Sequence;
using Xylia.Preview.Properties;

namespace Xylia.BnsHelper.Common.Helpers;
internal partial class SettingHelper() : IniSettings(Config)
{
    #region Constructor
    static string Config => Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments), "BnS", "settings.ini");

    public static SettingHelper Default { get; } = new SettingHelper();
    #endregion

    #region Application
    /// <summary>
    /// Gets or sets night mode.
    /// </summary>
    public int NightMode
    {
        get => GetValue<int>();
        set
        {
            SetValue(value);
            SkinHelpers.UpdateSkin(SkinType, value);
        }
    }

    /// <summary>
    /// Gets or sets skin type.
    /// </summary>
    public SkinType SkinType
    {
        get => (SkinType)GetValue<int>();
        set
        {
            SetValue((int)value);
            SkinHelpers.UpdateSkin(value, NightMode);
        }
    }

    public double FontSize { get => GetValue(12d); set => SetValue(value); }

    public bool AllowMessage { get => GetValue(true); set => SetValue(value); }

    public bool UseNotifyIcon { get => GetValue(true); set => SetValue(value); }

    public long Time { get => GetValue<long>(); set => SetValue(value); }

    /// <summary>
    /// Gets download folder.
    /// </summary>
    public string DownloadFolder
    {
        get
        {
            var folder = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "download");
            System.IO.Directory.CreateDirectory(folder);
            return folder;
        }
    }
    #endregion

    #region Game
    public event EventHandler? GameDirectoryChanged;

    private BnsGameInfo? _game;
    public BnsGameInfo Game
    {
        get
        {
            return _game ??= SettingPageViewModel.FindGameDirectory(GetValue<string>() ??
                throw new AppException(ExceptionCode.InvalidPath)).FirstOrDefault();
        }
        set
        {
            SetValue(_game = value);
            OnPropertyChanged(nameof(Servers));

            Server = null;
            ModRegister.Instance = null;
            GameDirectoryChanged?.Invoke(this, EventArgs.Empty);
        }
    }

    /// <summary>
    /// Gets safely the publisher of the game.
    /// </summary>
    internal EPublisher Publisher
    {
        get
        {
            try { return Game.Publisher; }
            catch { return EPublisher.ZTX; }
        }
    }

    public IEnumerable<BnsApiEndpoint> Servers
    {
        get
        {
            var data = new List<BnsApiEndpoint>();

            switch (Publisher)
            {
                case EPublisher.Tencent:
                    {
                        data.Add(BnsApiEndpoint.Tencent19);
                        data.Add(BnsApiEndpoint.Tencent20);
                        data.Add(BnsApiEndpoint.Tencent30);
                        data.Add(BnsApiEndpoint.Tencent90);
                        break;
                    }

                case EPublisher.ZTX:
                    {
                        data.Add(BnsApiEndpoint.Ztx10);
                        data.Add(BnsApiEndpoint.Ztx11);
                        data.Add(BnsApiEndpoint.Ztx12);
                        data.Add(BnsApiEndpoint.Ztx13);
                        if (RegistryHelper.Default.UseTestServer) data.Add(BnsApiEndpoint.Ztx90);
                        break;
                    }

                case EPublisher.ZNCG:
                    {
                        data.Add(BnsApiEndpoint.NeoTX1);
                        if (RegistryHelper.Default.UseTestServer) data.Add(BnsApiEndpoint.NeoTX9);
                        break;
                    }
            }

            return data;
        }
    }

    BnsApiEndpoint? _server;
    public BnsApiEndpoint? Server
    {
        get => _server ??= Servers.FirstOrDefault(x => x.Name == GetValue<string>()) ?? Servers.FirstOrDefault();
        set
        {
            SetValue(value);
            SetProperty(ref _server, value);
        }
    }

    public JobSeq Job => JobSeq.소환사;
    #endregion

    #region ACT
    public string HitFormat { get => GetValue(StringHelper.Get("DamageMeterPanel_Critical")!, section: "ACT"); set => SetValue(value, section: "ACT"); }

    public int AutoResetEncounter { get => GetValue(15, section: "ACT"); set => SetValue(value, section: "ACT"); }

    public bool EncounterMode { get => GetValue(false, section: "ACT"); set => SetValue(value, section: "ACT"); }

    public bool AllowCapture { get => GetValue(def: false, section: "ACT"); set => SetValue(value, section: "ACT"); }

    public bool GroupMode { get => GetValue(def: false, section: "ACT"); set => SetValue(value, section: "ACT"); }

    public bool DisableBattleLog { get => GetValue(def: false, section: "ACT"); set => SetValue(value, section: "ACT"); }
    #endregion
}

/// <summary>
/// Respent of setting option 
/// </summary>
public partial class SettingOption<T>(string key, T? def = default, string section = "Option",
    Action<object?>? OnChanged = null) : ObservableObject, ISettingOption
{
    public string? Text => StringHelper.Get($"Option_{key}");
    public string? Tooltip => StringHelper.Get($"Option_{key}_Tooltip");

    /// <summary>
    /// 获取按钮是否需要显示
    /// </summary>
    public bool Visiable => (Publisher?.Contains(SettingHelper.Default.Publisher) ?? true);

    public bool IsBool => typeof(T) == typeof(bool);
    public bool IsEnum => typeof(T).IsEnum;
    public bool IsInteger => typeof(T) == typeof(int);
    public EPublisher[]? Publisher { get; set; }


    /// <summary>
    /// Gets the value of option
    /// </summary>
    public object? Value
    {
        get => SettingHelper.Default.GetValue(def, key, section);
        set
        {
            SettingHelper.Default.SetValue(value, key, section);
            OnChanged?.Invoke(value);
        }
    }

    /// <summary>
    /// Gets all field of enum
    /// </summary>
    public object? Source
    {
        get
        {
            var type = typeof(T);
            return type.IsEnum ? Enum.GetValues(type) : null;
        }
    }

    /// <summary>
    /// Gets the status of option
    /// </summary>
    public bool IsActivate
    {
        get => Value is not null and not false;
        set
        {
            if (IsBool) Value = value;
            else if (!value) Value = default;
        }
    }

    public void Refresh()
    {
        OnPropertyChanged(nameof(Visiable));
    }
}

public interface ISettingOption
{
    bool Visiable { get; }

    void Refresh();
}
