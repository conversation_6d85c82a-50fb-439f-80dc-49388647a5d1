﻿<Page x:Class="Xylia.BnsHelper.Views.Pages.SettingPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
	  xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
	  xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" mc:Ignorable="d"
	  xmlns:hc="https://handyorg.github.io/handycontrol"
      xmlns:system="clr-namespace:System;assembly=mscorlib"
      xmlns:helper="clr-namespace:Xylia.BnsHelper.Common.Helpers">

    <Grid Margin="3 5">
        <Grid.Resources>
            <!-- Setting Item Style -->
            <Style x:Key="SettingItemStyle" TargetType="Grid">
                <Setter Property="Margin" Value="0,0,0,15" />
            </Style>

            <Style x:Key="SettingLabelStyle" TargetType="TextBlock">
                <Setter Property="FontSize" Value="13" />
                <Setter Property="FontWeight" Value="Medium" />
                <Setter Property="Foreground" Value="{DynamicResource PrimaryTextBrush}" />
                <Setter Property="VerticalAlignment" Value="Center" />
            </Style>

            <Style x:Key="SettingDescStyle" TargetType="TextBlock">
                <Setter Property="FontSize" Value="12" />
                <Setter Property="Foreground" Value="{DynamicResource SecondaryTextBrush}" />
                <Setter Property="Margin" Value="0,2,0,0" />
                <Setter Property="TextWrapping" Value="Wrap" />
                <Setter Property="Opacity" Value="0.85" />
            </Style>

            <!-- Default ToggleButton Style for this page -->
            <Style TargetType="ToggleButton" BasedOn="{StaticResource ToggleButtonSwitch}" />
        </Grid.Resources>

        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
        </Grid.RowDefinitions>

        <!-- TabControl -->
        <TabControl Grid.Row="1" Style="{StaticResource TabControlSliding}">
            <TabControl.Resources>
                <!-- Tab Content Container Style -->
                <Style TargetType="ScrollViewer">
                    <Setter Property="VerticalScrollBarVisibility" Value="Auto" />
                    <Setter Property="HorizontalScrollBarVisibility" Value="Disabled" />
                    <Setter Property="Padding" Value="0" />
                </Style>
            </TabControl.Resources>

            <!-- Basic Settings Tab -->
            <TabItem Header="{DynamicResource Settings_Group_Common}">
                <ScrollViewer>
                    <StackPanel Margin="15" MaxWidth="700">
                        <!-- Game Directory -->
                        <Grid Style="{StaticResource SettingItemStyle}">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="Auto" />
                            </Grid.ColumnDefinitions>
          
                            <StackPanel Grid.Column="0" Orientation="Horizontal" Height="25">
                                <TextBlock Style="{StaticResource SettingLabelStyle}" VerticalAlignment="Center">
                                    <Run Text="{DynamicResource Settings_GameDirectory}" />
                                    <Run Text="*" Foreground="OrangeRed" FontWeight="Bold" />
                                </TextBlock>
                            </StackPanel>
               
                            <Border Grid.Column="1" BorderBrush="{DynamicResource BorderBrush}"
                                    BorderThickness="1" CornerRadius="6" Background="{DynamicResource RegionBrush}" Width="260">
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*" />
                                        <ColumnDefinition Width="Auto" />
                                        <ColumnDefinition Width="Auto" />
                                    </Grid.ColumnDefinitions>

                                    <!-- ComboBox with custom style to remove border -->
                                    <ComboBox Grid.Column="0" ItemsSource="{Binding AvailableDirectories}"
                                              SelectedItem="{Binding Setting.Game}"
                                              BorderThickness="0" HorizontalContentAlignment="Stretch"
                                              Background="Transparent">
                                        <ComboBox.ItemTemplate>
                                            <DataTemplate>
                                                <Border Background="Transparent"
                                                        Padding="7,6,0,6"
                                                        CornerRadius="4">
                                                    <Grid>
                                                        <Grid.ColumnDefinitions>
                                                            <ColumnDefinition Width="Auto"/>
                                                            <ColumnDefinition Width="Auto"/>
                                                            <ColumnDefinition Width="*"/>
                                                            <ColumnDefinition Width="Auto"/>
                                                        </Grid.ColumnDefinitions>

                                                        <!-- FolderOpenGeometry 图标 -->
                                                        <Path Grid.Column="0"
                                                              Data="{StaticResource FolderOpenGeometry}"
                                                              Fill="{DynamicResource PrimaryTextBrush}"
                                                              Width="16" Height="16"
                                                              Stretch="Uniform"
                                                              VerticalAlignment="Center"
                                                              Margin="0,0,10,0"/>

                                                        <!-- 盘符 -->
                                                        <TextBlock Grid.Column="1"
                                                                   Text="{Binding Drive}"
                                                                   FontWeight="SemiBold"
                                                                   FontSize="13"
                                                                   VerticalAlignment="Center"
                                                                   Foreground="{DynamicResource PrimaryTextBrush}"
                                                                   Margin="0,0,8,0"/>

     
                                                            <!-- 游戏名称 -->
                                                            <TextBlock Grid.Column="2"
                                                                       Text="{Binding Name}"
                                                                       FontWeight="Medium"
                                                                       FontSize="12"
                                                                       Foreground="{DynamicResource PrimaryTextBrush}"
                                                                       Margin="0,0,0,1"
                                                                       VerticalAlignment="Center" />
                                          
                                                        <!-- 来源标识 -->
                                                        <Border Grid.Column="3"
                                                                Background="{DynamicResource SecondaryRegionBrush}"
                                                                CornerRadius="8"
                                                                Padding="5,2" HorizontalAlignment="Right"
                                                                VerticalAlignment="Center">
                                                            <TextBlock Text="{Binding Source}"
                                                                       FontSize="10"
                                                                       FontWeight="Medium"
                                                                       Foreground="{DynamicResource SecondaryTextBrush}"/>
                                                        </Border>
                                                    </Grid>
                                                </Border>
                                            </DataTemplate>
                                        </ComboBox.ItemTemplate>
                                        <ComboBox.ItemContainerStyle>
                                            <Style TargetType="ComboBoxItem" BasedOn="{StaticResource ComboBoxItemBaseStyle}">
                                                <Setter Property="ToolTip" Value="{Binding FullPath}" />
                                            </Style>
                                        </ComboBox.ItemContainerStyle>
                                    </ComboBox>

                                    <!-- 分隔线 -->
                                    <Rectangle Grid.Column="1" Width="1" Fill="{DynamicResource BorderBrush}" Margin="0,8,0,8" />

                                    <!-- 浏览按钮 -->
                                    <Button Grid.Column="2"
                                            Command="{Binding BrowseDirectoryCommand}"
                                            BorderThickness="0"
                                            Background="Transparent"
                                            Foreground="{DynamicResource PrimaryTextBrush}"
                                            ToolTip="浏览选择游戏目录"
                                            Cursor="Hand">
                                        <Button.Content>
                                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                                <!-- 文件夹图标 -->
                                                <Path Data="M10,4H4C2.89,4 2,4.89 2,6V18A2,2 0 0,0 4,20H20A2,2 0 0,0 22,18V8C22,6.89 21.1,6 20,6H12L10,4Z"
                                                      Fill="{Binding RelativeSource={RelativeSource AncestorType=Button}, Path=Foreground}"
                                                      Width="13" Height="13"
                                                      Stretch="Uniform"
                                                      VerticalAlignment="Center"
                                                      Margin="0,0,6,0"
                                                      Opacity="0.8"/>
                                                <TextBlock Text="浏览" FontSize="11" VerticalAlignment="Center" FontWeight="Medium"/>
                                            </StackPanel>
                                        </Button.Content>
                                        <Button.Style>
                                            <Style TargetType="Button">
                                                <Setter Property="Template">
                                                    <Setter.Value>
                                                        <ControlTemplate TargetType="Button">
                                                            <Border Background="{TemplateBinding Background}"
                                                                    CornerRadius="0,5,5,0"
                                                                    Padding="12,6">
                                                                <ContentPresenter HorizontalAlignment="Center"
                                                                                  VerticalAlignment="Center"/>
                                                            </Border>
                                                            <ControlTemplate.Triggers>
                                                                <Trigger Property="IsMouseOver" Value="True">
                                                                    <Setter Property="Background" Value="{DynamicResource SecondaryRegionBrush}"/>
                                                                    <Setter Property="Foreground" Value="{DynamicResource PrimaryBrush}"/>
                                                                </Trigger>
                                                                <Trigger Property="IsPressed" Value="True">
                                                                    <Setter Property="Background" Value="{DynamicResource BorderBrush}"/>
                                                                    <Setter Property="Foreground" Value="{DynamicResource PrimaryBrush}"/>
                                                                </Trigger>
                                                            </ControlTemplate.Triggers>
                                                        </ControlTemplate>
                                                    </Setter.Value>
                                                </Setter>
                                            </Style>
                                        </Button.Style>
                                    </Button>
                                </Grid>
                            </Border>
                        </Grid>

                        <!-- Server -->
                        <Grid Style="{StaticResource SettingItemStyle}">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="Auto" />
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="*" />
                                <RowDefinition Height="*" />
                            </Grid.RowDefinitions>

                            <StackPanel Grid.Column="0" Orientation="Horizontal">
                                <TextBlock Text="{DynamicResource Settings_Server}" Style="{StaticResource SettingLabelStyle}" />
                            </StackPanel>
                            <TextBlock Grid.Row="1" Text="选择启动游戏的大区" Style="{StaticResource SettingDescStyle}" />

                            <ComboBox Grid.RowSpan="99" Grid.Column="1" ItemsSource="{Binding Setting.Servers}" HorizontalAlignment="Right" HorizontalContentAlignment="Center"
                                      SelectedItem="{Binding Setting.Server,Delay=10}" Width="200" />
                        </Grid>

                        <!-- Night Mode -->
                        <Grid Style="{StaticResource SettingItemStyle}">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="Auto" />
                            </Grid.ColumnDefinitions>

                            <StackPanel Grid.Column="0">
                                <TextBlock Text="{DynamicResource Settings_NightMode}" Style="{StaticResource SettingLabelStyle}" />
                                <TextBlock Text="选择应用的主题模式" Style="{StaticResource SettingDescStyle}" />
                            </StackPanel>

                            <ComboBox Grid.Column="1" SelectedIndex="{Binding Setting.NightMode}" Width="200" HorizontalContentAlignment="Center">
                                <TextBlock>
                                    <Path Width="12" Height="12" Stretch="Fill" Margin="0 0 8 0" Fill="Gray" Data="{StaticResource DayGeometry}" VerticalAlignment="Center" />
                                    <Run Text="{DynamicResource Settings_NightMode_Auto}" />
                                </TextBlock>
                                <TextBlock>
                                    <Path Width="12" Height="12" Stretch="Fill" Margin="0 0 8 0" Fill="Gold" Data="{StaticResource DayGeometry}" VerticalAlignment="Center" />
                                    <Run Text="{DynamicResource Settings_NightMode_Day}" />
                                </TextBlock>
                                <TextBlock>
                                    <Path Width="12" Height="12" Stretch="Fill" Margin="0 0 8 0" Fill="SlateBlue" Data="{StaticResource NightGeometry}" VerticalAlignment="Center" />
                                    <Run Text="{DynamicResource Settings_NightMode_Night}" />
                                </TextBlock>
                            </ComboBox>
                        </Grid>

                        <!-- Theme Colors -->
                        <Grid Style="{StaticResource SettingItemStyle}">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="Auto" />
                            </Grid.ColumnDefinitions>

                            <StackPanel Grid.Column="0">
                                <TextBlock Text="{DynamicResource Settings_Skin}" Style="{StaticResource SettingLabelStyle}" />
                                <TextBlock Text="选择应用的主题颜色" Style="{StaticResource SettingDescStyle}" />
                            </StackPanel>

                            <ListBox Grid.Column="1" ItemsSource="{Binding Skins}" SelectedItem="{Binding SelectedSkin}" 
                                     Style="{x:Null}" BorderThickness="0" Background="Transparent" Margin="0,10,0,0" >
                                <ListBox.ItemsPanel>
                                    <ItemsPanelTemplate>
                                        <UniformGrid Columns="8" />
                                    </ItemsPanelTemplate>
                                </ListBox.ItemsPanel>
                                <ListBox.ItemContainerStyle>
                                    <Style TargetType="{x:Type ListBoxItem}">
                                        <Setter Property="Margin" Value="1.2" />
                                        <Setter Property="ToolTip" Value="{Binding .}" />
                                        <Setter Property="Template">
                                            <Setter.Value>
                                                <ControlTemplate TargetType="ListBoxItem">
                                                    <Border x:Name="PART_Border" Background="{Binding Brush}" Width="30" Height="21" CornerRadius="1" BorderBrush="{DynamicResource SecondaryBorderBrush}" />

                                                    <ControlTemplate.Triggers>
                                                        <Trigger Property="IsSelected" Value="True">
                                                            <Setter TargetName="PART_Border" Property="BorderThickness" Value="2" />
                                                        </Trigger>
                                                    </ControlTemplate.Triggers>
                                                </ControlTemplate>
                                            </Setter.Value>
                                        </Setter>
                                    </Style>
                                </ListBox.ItemContainerStyle>
                            </ListBox>
                        </Grid>

                        <!-- Font Size -->
                        <Grid Style="{StaticResource SettingItemStyle}">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="Auto" />
                            </Grid.ColumnDefinitions>

                            <StackPanel Grid.Column="0">
                                <TextBlock Text="{DynamicResource Settings_FontSize}" Style="{StaticResource SettingLabelStyle}" />
                                <TextBlock Text="调整应用界面的字体大小" Style="{StaticResource SettingDescStyle}" />
                            </StackPanel>

                            <Slider Grid.Column="1" IsSnapToTickEnabled="True" Minimum="10" Maximum="20"
                                    Value="{Binding Setting.FontSize}" TickFrequency="1" TickPlacement="BottomRight"
                                    Width="200" hc:TipElement.Visibility="Visible" hc:TipElement.StringFormat="#0"
                                    hc:TipElement.Placement="Top" />
                        </Grid>


                        <!-- Notify Icon -->
                        <Grid Style="{StaticResource SettingItemStyle}">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="Auto" />
                            </Grid.ColumnDefinitions>

                            <StackPanel Grid.Column="0">
                                <TextBlock Text="{DynamicResource Settings_ShowNotifyIcon}" Style="{StaticResource SettingLabelStyle}" />
                                <TextBlock Text="在系统托盘显示应用图标" Style="{StaticResource SettingDescStyle}" />
                            </StackPanel>

                            <ToggleButton Grid.Column="1" IsChecked="{Binding Setting.UseNotifyIcon}" />
                        </Grid>

                        <!-- Version Information -->
                        <Grid Style="{StaticResource SettingItemStyle}" Margin="0,10,0,0">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="Auto" />
                            </Grid.ColumnDefinitions>

                            <StackPanel Grid.Column="0">
                                <TextBlock Style="{StaticResource SettingLabelStyle}">
                                    <Run Text="版本信息" />
                                    <Hyperlink Command="hc:ControlCommands.OpenLink" CommandParameter="https://www.bnszs.com/" TextDecorations="None">©剑灵小助手</Hyperlink>
                                </TextBlock>
                                <TextBlock Margin="0,8,0,0" FontSize="12" FontWeight="Medium" Foreground="{DynamicResource SecondaryTextBrush}">
                                    <Run Text="当前版本" />
                                    <Run Text="{Binding Source={x:Static helper:VersionHelper.Version},Mode=OneWay}" />
                                    <Run Text="插件版本" />
                                    <Run Text="{Binding PluginVersion,Mode=OneWay}" />
                                </TextBlock>
                            </StackPanel>

                            <Button Grid.Column="1" Content="{DynamicResource About_Name}"
                                    Command="{Binding OpenAboutViewCommand}"
                                    Style="{StaticResource ButtonDefault}"
                                    Padding="12,6" VerticalAlignment="Center" />
                        </Grid>
                    </StackPanel>
                </ScrollViewer>
            </TabItem>

            <!-- ACT Settings Tab -->
            <TabItem Header="{DynamicResource Settings_Group_ACT}">
                <ScrollViewer>
                    <StackPanel Margin="15" MaxWidth="700">
                        <!-- Hit Format -->
                        <Grid Style="{StaticResource SettingItemStyle}">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="Auto" />
                            </Grid.ColumnDefinitions>

                            <StackPanel Grid.Column="0">
                                <TextBlock Text="{DynamicResource Settings_HitFormat}" Style="{StaticResource SettingLabelStyle}" />
                                <TextBlock Text="设置命中次数显示格式" Style="{StaticResource SettingDescStyle}" />
                            </StackPanel>

                            <ComboBox Grid.Column="1" Text="{Binding Setting.HitFormat}" Width="200" IsEditable="True">
                                <ComboBoxItem Content="命中 (暴击率)" />
                                <ComboBoxItem Content="闪避 (暴击率)" />
                            </ComboBox>
                        </Grid>

                        <!-- Auto Reset Encounter -->
                        <Grid Style="{StaticResource SettingItemStyle}">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="Auto" />
                            </Grid.ColumnDefinitions>

                            <StackPanel Grid.Column="0">
                                <TextBlock Text="{DynamicResource Settings_AutoResetEncounter}" Style="{StaticResource SettingLabelStyle}" />
                                <TextBlock Text="设置自动重置战斗统计的秒数" Style="{StaticResource SettingDescStyle}" />
                            </StackPanel>

                            <hc:NumericUpDown Grid.Column="1" Minimum="5" Maximum="100"
                                              Value="{Binding Setting.AutoResetEncounter}" Width="120" />
                        </Grid>

                        <!-- Encounter Mode -->
                        <Grid Style="{StaticResource SettingItemStyle}">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="Auto" />
                            </Grid.ColumnDefinitions>

                            <StackPanel Grid.Column="0">
                                <TextBlock Text="{DynamicResource Settings_EncounterMode}" Style="{StaticResource SettingLabelStyle}" />
                                <TextBlock Text="设置统计模式" Style="{StaticResource SettingDescStyle}" />
                            </StackPanel>

                            <ToggleButton Grid.Column="1" IsChecked="{Binding Setting.EncounterMode}" />
                        </Grid>

                        <!-- Allow Message -->
                        <Grid Style="{StaticResource SettingItemStyle}">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="Auto" />
                            </Grid.ColumnDefinitions>

                            <StackPanel Grid.Column="0">
                                <TextBlock Text="{DynamicResource Settings_AllowMessage}" Style="{StaticResource SettingLabelStyle}" />
                                <TextBlock Text="允许向游戏发送消息通知" Style="{StaticResource SettingDescStyle}" />
                            </StackPanel>

                            <ToggleButton Grid.Column="1" IsChecked="{Binding Setting.AllowMessage}" />
                        </Grid>

                        <!-- Disable Battle Log -->
                        <Grid Style="{StaticResource SettingItemStyle}">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="Auto" />
                            </Grid.ColumnDefinitions>

                            <StackPanel Grid.Column="0">
                                <TextBlock Text="禁用战斗记录日志" Style="{StaticResource SettingLabelStyle}" />
                                <TextBlock Text="勾选后将不生成战斗记录日志文件" Style="{StaticResource SettingDescStyle}" />
                            </StackPanel>

                            <ToggleButton Grid.Column="1" IsChecked="{Binding Setting.DisableBattleLog}" />
                        </Grid>

                        <!-- Allow Capture -->
                        <Grid Style="{StaticResource SettingItemStyle}">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="Auto" />
                            </Grid.ColumnDefinitions>

                            <StackPanel Grid.Column="0">
                                <TextBlock Text="{DynamicResource Settings_AllowCapture}" Style="{StaticResource SettingLabelStyle}" />
                                <TextBlock Text="允许录制战斗统计界面" Style="{StaticResource SettingDescStyle}" />
                            </StackPanel>

                            <ToggleButton Grid.Column="1" IsChecked="{Binding Setting.AllowCapture}" />
                        </Grid>
                    </StackPanel>
                </ScrollViewer>
            </TabItem>

            <!-- Keyboard Shortcuts Tab -->
            <TabItem Header="{DynamicResource Settings_Group_Command}">
                <ScrollViewer>
                    <StackPanel Margin="15" MaxWidth="700">
                        <!-- Reset ACT -->
                        <Grid Style="{StaticResource SettingItemStyle}">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="Auto" />
                            </Grid.ColumnDefinitions>

                            <StackPanel Grid.Column="0">
                                <TextBlock Text="{DynamicResource Settings_Command_ResetACT}" Style="{StaticResource SettingLabelStyle}" />
                                <TextBlock Text="重置战斗记录统计数据" Style="{StaticResource SettingDescStyle}" />
                            </StackPanel>

                            <Border Grid.Column="1" Background="{DynamicResource SecondaryRegionBrush}"
                                    BorderBrush="{DynamicResource BorderBrush}" BorderThickness="1"
                                    CornerRadius="4" Padding="8,4" MinWidth="80">
                                <TextBlock Text="F1" FontFamily="Consolas" FontWeight="Medium"
                                           HorizontalAlignment="Center" VerticalAlignment="Center"
                                           Foreground="{DynamicResource PrimaryTextBrush}" />
                            </Border>
                        </Grid>

                        <!-- Hide Custom UI -->
                        <Grid Style="{StaticResource SettingItemStyle}">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="Auto" />
                            </Grid.ColumnDefinitions>

                            <StackPanel Grid.Column="0">
                                <TextBlock Text="{DynamicResource Settings_Command_HideCustomUI}" Style="{StaticResource SettingLabelStyle}" />
                                <TextBlock Text="后续会增加详细隐藏设置" Style="{StaticResource SettingDescStyle}" />
                            </StackPanel>

                            <Border Grid.Column="1" Background="{DynamicResource SecondaryRegionBrush}"
                                    BorderBrush="{DynamicResource BorderBrush}" BorderThickness="1"
                                    CornerRadius="4" Padding="8,4" MinWidth="80">
                                <TextBlock Text="Alt + X" FontFamily="Consolas" FontWeight="Medium"
                                           HorizontalAlignment="Center" VerticalAlignment="Center"
                                           Foreground="{DynamicResource PrimaryTextBrush}" />
                            </Border>
                        </Grid>

                        <!-- Clear Memory -->
                        <Grid Style="{StaticResource SettingItemStyle}">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="Auto" />
                            </Grid.ColumnDefinitions>

                            <StackPanel Grid.Column="0">
                                <TextBlock Text="{DynamicResource Settings_Command_ClearMemory}" Style="{StaticResource SettingLabelStyle}" />
                                <TextBlock Text="清理游戏的虚拟内存" Style="{StaticResource SettingDescStyle}" />
                            </StackPanel>

                            <Border Grid.Column="1" Background="{DynamicResource SecondaryRegionBrush}"
                                    BorderBrush="{DynamicResource BorderBrush}" BorderThickness="1"
                                    CornerRadius="4" Padding="8,4" MinWidth="80">
                                <TextBlock Text="Alt + F2" FontFamily="Consolas" FontWeight="Medium"
                                           HorizontalAlignment="Center" VerticalAlignment="Center"
                                           Foreground="{DynamicResource PrimaryTextBrush}" />
                            </Border>
                        </Grid>
                    </StackPanel>
                </ScrollViewer>
            </TabItem>
        </TabControl>
    </Grid>
</Page>